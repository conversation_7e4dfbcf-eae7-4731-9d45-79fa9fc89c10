# PowerShell script to download required dependencies
$libDir = "lib"
if (!(Test-Path $libDir)) {
    New-Item -ItemType Directory -Path $libDir
}

# Selenium Java 4.34.0 - try different approach with individual components
$seleniumApiUrl = "https://repo1.maven.org/maven2/org/seleniumhq/selenium/selenium-api/4.34.0/selenium-api-4.34.0.jar"
$seleniumApiFile = "$libDir/selenium-api-4.34.0.jar"

$seleniumChromeUrl = "https://repo1.maven.org/maven2/org/seleniumhq/selenium/selenium-chrome-driver/4.34.0/selenium-chrome-driver-4.34.0.jar"
$seleniumChromeFile = "$libDir/selenium-chrome-driver-4.34.0.jar"

$seleniumSupportUrl = "https://repo1.maven.org/maven2/org/seleniumhq/selenium/selenium-support/4.34.0/selenium-support-4.34.0.jar"
$seleniumSupportFile = "$libDir/selenium-support-4.34.0.jar"

$seleniumRemoteUrl = "https://repo1.maven.org/maven2/org/seleniumhq/selenium/selenium-remote-driver/4.34.0/selenium-remote-driver-4.34.0.jar"
$seleniumRemoteFile = "$libDir/selenium-remote-driver-4.34.0.jar"

$seleniumChromiumUrl = "https://repo1.maven.org/maven2/org/seleniumhq/selenium/selenium-chromium-driver/4.34.0/selenium-chromium-driver-4.34.0.jar"
$seleniumChromiumFile = "$libDir/selenium-chromium-driver-4.34.0.jar"

# Apache POI 5.4.1
$poiUrl = "https://repo1.maven.org/maven2/org/apache/poi/poi/5.4.1/poi-5.4.1.jar"
$poiFile = "$libDir/poi-5.4.1.jar"

$poiOoxmlUrl = "https://repo1.maven.org/maven2/org/apache/poi/poi-ooxml/5.4.1/poi-ooxml-5.4.1.jar"
$poiOoxmlFile = "$libDir/poi-ooxml-5.4.1.jar"

Write-Host "Downloading Selenium API..."
try {
    Invoke-WebRequest -Uri $seleniumApiUrl -OutFile $seleniumApiFile
    Write-Host "Downloaded: $seleniumApiFile"
} catch {
    Write-Host "Failed to download Selenium API: $_"
}

Write-Host "Downloading Selenium Chrome Driver..."
try {
    Invoke-WebRequest -Uri $seleniumChromeUrl -OutFile $seleniumChromeFile
    Write-Host "Downloaded: $seleniumChromeFile"
} catch {
    Write-Host "Failed to download Selenium Chrome: $_"
}

Write-Host "Downloading Selenium Support..."
try {
    Invoke-WebRequest -Uri $seleniumSupportUrl -OutFile $seleniumSupportFile
    Write-Host "Downloaded: $seleniumSupportFile"
} catch {
    Write-Host "Failed to download Selenium Support: $_"
}

Write-Host "Downloading Selenium Remote Driver..."
try {
    Invoke-WebRequest -Uri $seleniumRemoteUrl -OutFile $seleniumRemoteFile
    Write-Host "Downloaded: $seleniumRemoteFile"
} catch {
    Write-Host "Failed to download Selenium Remote: $_"
}

Write-Host "Downloading Selenium Chromium Driver..."
try {
    Invoke-WebRequest -Uri $seleniumChromiumUrl -OutFile $seleniumChromiumFile
    Write-Host "Downloaded: $seleniumChromiumFile"
} catch {
    Write-Host "Failed to download Selenium Chromium: $_"
}

Write-Host "Downloading Apache POI..."
try {
    Invoke-WebRequest -Uri $poiUrl -OutFile $poiFile
    Write-Host "Downloaded: $poiFile"
} catch {
    Write-Host "Failed to download POI: $_"
}

Write-Host "Downloading Apache POI OOXML..."
try {
    Invoke-WebRequest -Uri $poiOoxmlUrl -OutFile $poiOoxmlFile
    Write-Host "Downloaded: $poiOoxmlFile"
} catch {
    Write-Host "Failed to download POI OOXML: $_"
}

Write-Host "Dependencies download completed!"
