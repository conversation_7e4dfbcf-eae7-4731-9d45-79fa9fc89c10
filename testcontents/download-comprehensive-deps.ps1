# PowerShell script to download comprehensive dependencies
$libDir = "lib"
if (!(Test-Path $libDir)) {
    New-Item -ItemType Directory -Path $libDir
}

# Download comprehensive Selenium dependencies
$dependencies = @(
    @{
        name = "selenium-java"
        url = "https://repo1.maven.org/maven2/org/seleniumhq/selenium/selenium-java/4.34.0/selenium-java-4.34.0.jar"
        file = "selenium-java-4.34.0.jar"
    },
    @{
        name = "selenium-api"
        url = "https://repo1.maven.org/maven2/org/seleniumhq/selenium/selenium-api/4.34.0/selenium-api-4.34.0.jar"
        file = "selenium-api-4.34.0.jar"
    },
    @{
        name = "selenium-chrome-driver"
        url = "https://repo1.maven.org/maven2/org/seleniumhq/selenium/selenium-chrome-driver/4.34.0/selenium-chrome-driver-4.34.0.jar"
        file = "selenium-chrome-driver-4.34.0.jar"
    },
    @{
        name = "selenium-chromium-driver"
        url = "https://repo1.maven.org/maven2/org/seleniumhq/selenium/selenium-chromium-driver/4.34.0/selenium-chromium-driver-4.34.0.jar"
        file = "selenium-chromium-driver-4.34.0.jar"
    },
    @{
        name = "selenium-remote-driver"
        url = "https://repo1.maven.org/maven2/org/seleniumhq/selenium/selenium-remote-driver/4.34.0/selenium-remote-driver-4.34.0.jar"
        file = "selenium-remote-driver-4.34.0.jar"
    },
    @{
        name = "selenium-support"
        url = "https://repo1.maven.org/maven2/org/seleniumhq/selenium/selenium-support/4.34.0/selenium-support-4.34.0.jar"
        file = "selenium-support-4.34.0.jar"
    },
    @{
        name = "selenium-http"
        url = "https://repo1.maven.org/maven2/org/seleniumhq/selenium/selenium-http/4.34.0/selenium-http-4.34.0.jar"
        file = "selenium-http-4.34.0.jar"
    },
    @{
        name = "selenium-manager"
        url = "https://repo1.maven.org/maven2/org/seleniumhq/selenium/selenium-manager/4.34.0/selenium-manager-4.34.0.jar"
        file = "selenium-manager-4.34.0.jar"
    },
    @{
        name = "poi"
        url = "https://repo1.maven.org/maven2/org/apache/poi/poi/5.4.1/poi-5.4.1.jar"
        file = "poi-5.4.1.jar"
    },
    @{
        name = "poi-ooxml"
        url = "https://repo1.maven.org/maven2/org/apache/poi/poi-ooxml/5.4.1/poi-ooxml-5.4.1.jar"
        file = "poi-ooxml-5.4.1.jar"
    },
    @{
        name = "poi-ooxml-schemas"
        url = "https://repo1.maven.org/maven2/org/apache/poi/poi-ooxml-schemas/5.4.1/poi-ooxml-schemas-5.4.1.jar"
        file = "poi-ooxml-schemas-5.4.1.jar"
    },
    @{
        name = "commons-compress"
        url = "https://repo1.maven.org/maven2/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar"
        file = "commons-compress-1.27.1.jar"
    },
    @{
        name = "xmlbeans"
        url = "https://repo1.maven.org/maven2/org/apache/xmlbeans/xmlbeans/5.2.1/xmlbeans-5.2.1.jar"
        file = "xmlbeans-5.2.1.jar"
    }
)

foreach ($dep in $dependencies) {
    $filePath = "$libDir/$($dep.file)"
    if (!(Test-Path $filePath)) {
        Write-Host "Downloading $($dep.name)..."
        try {
            Invoke-WebRequest -Uri $dep.url -OutFile $filePath
            Write-Host "Downloaded: $filePath"
        } catch {
            Write-Host "Failed to download $($dep.name): $_"
        }
    } else {
        Write-Host "Already exists: $filePath"
    }
}

Write-Host "Dependencies download completed!"
